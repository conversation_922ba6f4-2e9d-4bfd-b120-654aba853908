# Gemini AI Test Interface

This directory contains test interfaces for various AI services and features.

## Available Test Pages

### 1. Gemini AI Test (`/test/gemini-ai`)

A comprehensive test interface for the Google Gemini AI service that allows you to:

#### Text Generation Features:
- **Basic Text Generation**: Generate text with customizable prompts
- **Model Selection**: Choose between different Gemini models
- **System Instructions**: Set custom behavior instructions
- **Temperature Control**: Adjust creativity level (0-1)
- **Token Limits**: Control response length
- **Quick Examples**: Pre-built prompts for common use cases

#### Image Generation Features:
- **Gemini Native**: Contextual image generation with optional text responses
- **Imagen 3**: High-quality, artistic image generation
- **Aspect Ratio Selection**: Multiple format options (1:1, 3:4, 4:3, 9:16, 16:9)
- **Batch Generation**: Generate multiple images at once (Imagen 3)
- **Image Download**: Download generated images as PNG files

#### Service Monitoring:
- **Health Check**: Real-time service status monitoring
- **Error Handling**: Detailed error messages and troubleshooting
- **Performance Metrics**: Generation time and response metadata

## How to Access

### For Administrators:
1. Sign in to your admin account
2. Navigate to the Admin Panel
3. Click on "Gemini AI Test" in the sidebar
4. Start testing text and image generation

### Direct URL:
Visit `/test/gemini-ai` directly if you have appropriate permissions.

## API Endpoints

The test interface uses these API endpoints:

- `POST /api/ai/generate-text` - Text generation
- `POST /api/ai/generate-image` - Image generation  
- `GET /api/ai/health-check` - Service health status
- `GET /api/ai/templates` - Available prompt templates

## Quick Start Examples

### Text Generation Examples:
1. **Event Description**: "Write an engaging description for a birthday party event happening this Saturday at Central Park..."
2. **Invitation Message**: "Create a personalized invitation message for Sarah's wedding reception..."
3. **Event Reminder**: "Generate a friendly reminder message for a corporate networking event..."

### Image Generation Examples:
1. **Birthday Invitation**: "Elegant birthday party invitation with gold accents, modern minimalist design..."
2. **Wedding Background**: "Wedding reception background with romantic floral elements..."
3. **Corporate Event**: "Corporate event poster design, professional blue and white theme..."

## Configuration

Make sure you have the following environment variable set:

```bash
GEMINI_API_KEY=your_google_gemini_api_key_here
```

Get your API key from [Google AI Studio](https://aistudio.google.com/apikey).

## Features

### Text Generation:
- ✅ Multiple Gemini models support
- ✅ Customizable system instructions
- ✅ Temperature and token control
- ✅ Real-time generation
- ✅ Copy to clipboard functionality
- ✅ Pre-built prompt templates

### Image Generation:
- ✅ Gemini native image generation
- ✅ Imagen 3 high-quality generation
- ✅ Multiple aspect ratios
- ✅ Batch image generation
- ✅ Image download functionality
- ✅ Base64 image display

### User Experience:
- ✅ Responsive design
- ✅ Loading states and progress indicators
- ✅ Error handling with user-friendly messages
- ✅ Quick example buttons
- ✅ Service health monitoring
- ✅ Toast notifications for feedback

## Troubleshooting

### Common Issues:

1. **"Service is unhealthy"**
   - Check your GEMINI_API_KEY environment variable
   - Verify the API key is valid and has proper permissions
   - Check your internet connection

2. **"Rate limit exceeded"**
   - Wait a few minutes before trying again
   - Consider upgrading your API quota if needed

3. **"Image generation not available"**
   - Image generation may not be available in all regions
   - Try using text generation first to verify the service works

4. **"Failed to generate content"**
   - Check the prompt length (should be under 8000 characters)
   - Ensure the prompt doesn't contain inappropriate content
   - Try a simpler prompt to test the service

### Getting Help:

1. Check the service health status first
2. Try the quick examples to verify basic functionality
3. Review the browser console for detailed error messages
4. Check the API endpoint responses for specific error details

## Development Notes

- The test interface is built with Next.js and React
- Uses the custom Gemini service from `/lib/gemini`
- Implements proper error handling and user feedback
- Follows the existing UI component patterns
- Includes comprehensive logging for debugging
