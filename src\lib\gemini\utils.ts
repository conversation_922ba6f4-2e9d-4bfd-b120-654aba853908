import { PromptTemplate, ProcessedTemplate, PROMPT_TEMPLATES, IMAGE_PROMPT_TEMPLATES } from './types';

/**
 * Utility functions for Gemini AI service
 */

/**
 * Process a prompt template by replacing variables with provided values
 */
export function processPromptTemplate(
  templateName: string,
  variables: Record<string, string>,
  isImageTemplate = false
): ProcessedTemplate {
  const templates = isImageTemplate ? IMAGE_PROMPT_TEMPLATES : PROMPT_TEMPLATES;
  const template = templates.find(t => t.name === templateName);
  
  if (!template) {
    throw new Error(`Template '${templateName}' not found`);
  }

  let content = template.template;
  const missingVariables: string[] = [];

  // Replace variables in the template
  for (const variable of template.variables) {
    const placeholder = `{${variable}}`;
    if (variables[variable] !== undefined) {
      content = content.replace(new RegExp(placeholder, 'g'), variables[variable]);
    } else {
      missingVariables.push(variable);
    }
  }

  return {
    content,
    missingVariables
  };
}

/**
 * Get all available prompt templates
 */
export function getPromptTemplates(): PromptTemplate[] {
  return PROMPT_TEMPLATES;
}

/**
 * Get all available image prompt templates
 */
export function getImagePromptTemplates(): PromptTemplate[] {
  return IMAGE_PROMPT_TEMPLATES;
}

/**
 * Get a specific template by name
 */
export function getTemplate(name: string, isImageTemplate = false): PromptTemplate | undefined {
  const templates = isImageTemplate ? IMAGE_PROMPT_TEMPLATES : PROMPT_TEMPLATES;
  return templates.find(t => t.name === name);
}

/**
 * Validate that all required variables are provided for a template
 */
export function validateTemplateVariables(
  templateName: string,
  variables: Record<string, string>,
  isImageTemplate = false
): { isValid: boolean; missingVariables: string[] } {
  const template = getTemplate(templateName, isImageTemplate);
  
  if (!template) {
    throw new Error(`Template '${templateName}' not found`);
  }

  const missingVariables = template.variables.filter(
    variable => variables[variable] === undefined || variables[variable] === ''
  );

  return {
    isValid: missingVariables.length === 0,
    missingVariables
  };
}

/**
 * Generate a prompt for event description
 */
export function generateEventDescriptionPrompt(options: {
  eventType: string;
  eventTitle: string;
  eventDate: string;
  eventLocation: string;
  additionalDetails?: string;
}): string {
  const { eventType, eventTitle, eventDate, eventLocation, additionalDetails = '' } = options;
  
  return processPromptTemplate('event_description', {
    eventType,
    eventTitle,
    eventDate,
    eventLocation,
    additionalDetails
  }).content;
}

/**
 * Generate a prompt for invitation message
 */
export function generateInvitationMessagePrompt(options: {
  guestName: string;
  eventTitle: string;
  eventType: string;
  eventDate: string;
  eventLocation: string;
  personalNote?: string;
}): string {
  const { guestName, eventTitle, eventType, eventDate, eventLocation, personalNote = '' } = options;
  
  return processPromptTemplate('invitation_message', {
    guestName,
    eventTitle,
    eventType,
    eventDate,
    eventLocation,
    personalNote
  }).content;
}

/**
 * Generate a prompt for event reminder
 */
export function generateEventReminderPrompt(options: {
  eventTitle: string;
  timeUntilEvent: string;
  eventLocation: string;
  eventTime: string;
  importantNotes?: string;
}): string {
  const { eventTitle, timeUntilEvent, eventLocation, eventTime, importantNotes = '' } = options;
  
  return processPromptTemplate('event_reminder', {
    eventTitle,
    timeUntilEvent,
    eventLocation,
    eventTime,
    importantNotes
  }).content;
}

/**
 * Generate a prompt for thank you message
 */
export function generateThankYouMessagePrompt(options: {
  eventTitle: string;
  eventHighlights: string;
  followUpInfo?: string;
}): string {
  const { eventTitle, eventHighlights, followUpInfo = '' } = options;
  
  return processPromptTemplate('thank_you_message', {
    eventTitle,
    eventHighlights,
    followUpInfo
  }).content;
}

/**
 * Generate a prompt for event invitation image
 */
export function generateEventInvitationImagePrompt(options: {
  eventType: string;
  styleDescription: string;
  moodDescription: string;
  aestheticStyle: string;
}): string {
  const { eventType, styleDescription, moodDescription, aestheticStyle } = options;
  
  return processPromptTemplate('event_invitation_image', {
    eventType,
    styleDescription,
    moodDescription,
    aestheticStyle
  }, true).content;
}

/**
 * Generate a prompt for event background image
 */
export function generateEventBackgroundImagePrompt(options: {
  eventType: string;
  colorScheme: string;
  atmosphereDescription: string;
  designStyle: string;
}): string {
  const { eventType, colorScheme, atmosphereDescription, designStyle } = options;
  
  return processPromptTemplate('event_background', {
    eventType,
    colorScheme,
    atmosphereDescription,
    designStyle
  }, true).content;
}

/**
 * Generate a prompt for social media post image
 */
export function generateSocialMediaImagePrompt(options: {
  eventType: string;
  designStyle: string;
  colorPalette: string;
  eventTheme: string;
  platform: string;
}): string {
  const { eventType, designStyle, colorPalette, eventTheme, platform } = options;
  
  return processPromptTemplate('social_media_post', {
    eventType,
    designStyle,
    colorPalette,
    eventTheme,
    platform
  }, true).content;
}

/**
 * Sanitize and validate prompt input
 */
export function sanitizePrompt(prompt: string): string {
  // Remove potentially harmful content
  return prompt
    .trim()
    .replace(/[<>]/g, '') // Remove HTML-like tags
    .substring(0, 8000); // Limit length
}

/**
 * Get optimal image aspect ratio based on use case
 */
export function getOptimalAspectRatio(useCase: string): "1:1" | "3:4" | "4:3" | "9:16" | "16:9" {
  switch (useCase.toLowerCase()) {
    case 'instagram_post':
    case 'profile_picture':
    case 'logo':
      return "1:1";
    case 'instagram_story':
    case 'mobile_wallpaper':
      return "9:16";
    case 'facebook_cover':
    case 'twitter_header':
    case 'website_banner':
      return "16:9";
    case 'print_invitation':
    case 'poster':
      return "3:4";
    case 'landscape_photo':
    case 'presentation':
      return "4:3";
    default:
      return "1:1";
  }
}

/**
 * Estimate token count for a prompt (rough estimation)
 */
export function estimateTokenCount(text: string): number {
  // Rough estimation: 1 token ≈ 4 characters for English text
  return Math.ceil(text.length / 4);
}

/**
 * Check if prompt is within token limits
 */
export function isPromptWithinLimits(prompt: string, maxTokens = 8000): boolean {
  return estimateTokenCount(prompt) <= maxTokens;
}
