import { GoogleGenAI, Modality } from "@google/genai";
import { debugLog } from "../logger";
import { envvar } from "../utils";

/**
 * Google Gemini AI Service
 *
 * This service provides text generation and image generation capabilities
 * using Google's Gemini API. It supports both Gemini native image generation
 * and Imagen 3 for specialized high-quality image generation.
 */
export class GeminiService {
  private ai: GoogleGenAI;
  private apiKey: string;

  constructor() {
    this.apiKey = envvar('GEMINI_API_KEY');
    this.ai = new GoogleGenAI({ apiKey: this.apiKey });
    debugLog('GeminiService initialized', { hasApiKey: !!this.apiKey });
  }

  /**
   * Generate text content using Gemini models
   */
  async generateText(options: {
    prompt: string;
    model?: string;
    systemInstruction?: string;
    temperature?: number;
    maxOutputTokens?: number;
    streaming?: boolean;
  }) {
    const {
      prompt,
      model = "gemini-2.0-flash",
      systemInstruction,
      temperature = 0.7,
      maxOutputTokens = 1000,
      streaming = false
    } = options;

    try {
      debugLog('Generating text with Gemini', {
        model,
        promptLength: prompt.length,
        temperature,
        maxOutputTokens,
        streaming
      });

      const config = {
        systemInstruction,
        temperature,
        maxOutputTokens,
      };

      if (streaming) {
        const response = await this.ai.models.generateContentStream({
          model,
          contents: prompt,
          config,
        });

        return response;
      } else {
        const response = await this.ai.models.generateContent({
          model,
          contents: prompt,
          config,
        });

        debugLog('Text generation completed', {
          responseLength: response.text?.length || 0
        });

        return response;
      }
    } catch (error) {
      debugLog('Error generating text', { error });
      throw new Error(`Failed to generate text: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate text with chat conversation context
   */
  async generateChatResponse(options: {
    message: string;
    history?: Array<{ role: 'user' | 'model'; parts: Array<{ text: string }> }>;
    model?: string;
    systemInstruction?: string;
  }) {
    const {
      message,
      history = [],
      model = "gemini-2.0-flash",
      systemInstruction
    } = options;

    try {
      debugLog('Generating chat response', {
        model,
        messageLength: message.length,
        historyLength: history.length
      });

      const chat = this.ai.chats.create({
        model,
        history,
        config: {
          systemInstruction,
        },
      });

      const response = await chat.sendMessage({
        message,
      });

      debugLog('Chat response generated', {
        responseLength: response.text?.length || 0
      });

      return response;
    } catch (error) {
      debugLog('Error generating chat response', { error });
      throw new Error(`Failed to generate chat response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate images using Gemini native image generation
   * Best for contextual images and conversational editing
   */
  async generateImageWithGemini(options: {
    prompt: string;
    model?: string;
    includeText?: boolean;
  }) {
    const {
      prompt,
      model = "gemini-2.0-flash-preview-image-generation",
      includeText = true
    } = options;

    try {
      debugLog('Generating image with Gemini native', {
        model,
        promptLength: prompt.length,
        includeText
      });

      const responseModalities = includeText
        ? [Modality.TEXT, Modality.IMAGE]
        : [Modality.IMAGE];

      const response = await this.ai.models.generateContent({
        model,
        contents: prompt,
        config: {
          responseModalities,
        },
      });

      const result = {
        text: null as string | null,
        images: [] as Buffer[],
      };

      for (const part of response.candidates?.[0]?.content?.parts || []) {
        if (part.text) {
          result.text = part.text;
        } else if (part.inlineData) {
          const imageBuffer = Buffer.from(part.inlineData.data, "base64");
          result.images.push(imageBuffer);
        }
      }

      debugLog('Gemini image generation completed', {
        hasText: !!result.text,
        imageCount: result.images.length
      });

      return result;
    } catch (error) {
      debugLog('Error generating image with Gemini', { error });
      throw new Error(`Failed to generate image with Gemini: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Edit images using Gemini native capabilities
   */
  async editImageWithGemini(options: {
    prompt: string;
    imageData: Buffer;
    mimeType: string;
    model?: string;
  }) {
    const {
      prompt,
      imageData,
      mimeType,
      model = "gemini-2.0-flash-preview-image-generation"
    } = options;

    try {
      debugLog('Editing image with Gemini', {
        model,
        promptLength: prompt.length,
        imageSize: imageData.length,
        mimeType
      });

      const base64Image = imageData.toString("base64");

      const contents = [
        { text: prompt },
        {
          inlineData: {
            mimeType,
            data: base64Image,
          },
        },
      ];

      const response = await this.ai.models.generateContent({
        model,
        contents,
        config: {
          responseModalities: [Modality.TEXT, Modality.IMAGE],
        },
      });

      const result = {
        text: null as string | null,
        images: [] as Buffer[],
      };

      for (const part of response.candidates?.[0]?.content?.parts || []) {
        if (part.text) {
          result.text = part.text;
        } else if (part.inlineData) {
          const imageBuffer = Buffer.from(part.inlineData.data, "base64");
          result.images.push(imageBuffer);
        }
      }

      debugLog('Gemini image editing completed', {
        hasText: !!result.text,
        imageCount: result.images.length
      });

      return result;
    } catch (error) {
      debugLog('Error editing image with Gemini', { error });
      throw new Error(`Failed to edit image with Gemini: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate images using Imagen 3
   * Best for high-quality, artistic, and specialized image generation
   */
  async generateImageWithImagen(options: {
    prompt: string;
    numberOfImages?: number;
    aspectRatio?: "1:1" | "3:4" | "4:3" | "9:16" | "16:9";
    personGeneration?: "dont_allow" | "allow_adult" | "allow_all";
    model?: string;
  }) {
    const {
      prompt,
      numberOfImages = 1,
      aspectRatio = "1:1",
      personGeneration = "allow_adult",
      model = "imagen-3.0-generate-002"
    } = options;

    try {
      debugLog('Generating image with Imagen 3', {
        model,
        promptLength: prompt.length,
        numberOfImages,
        aspectRatio,
        personGeneration
      });

      const response = await this.ai.models.generateImages({
        model,
        prompt,
        config: {
          numberOfImages,
          aspectRatio,
          personGeneration,
        },
      });

      const images: Buffer[] = [];
      for (const generatedImage of response.generatedImages || []) {
        if (generatedImage.image?.imageBytes) {
          const imageBuffer = Buffer.from(generatedImage.image.imageBytes, "base64");
          images.push(imageBuffer);
        }
      }

      debugLog('Imagen 3 generation completed', {
        imageCount: images.length
      });

      return images;
    } catch (error) {
      debugLog('Error generating image with Imagen 3', { error });
      throw new Error(`Failed to generate image with Imagen 3: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Health check for the service
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Simple test to verify the API key works
      const response = await this.generateText({
        prompt: "Hello",
        maxOutputTokens: 10,
      });

      return !!response.text;
    } catch (error) {
      debugLog('Gemini service health check failed', { error });
      return false;
    }
  }
}

// Export singleton instance
export const geminiService = new GeminiService();