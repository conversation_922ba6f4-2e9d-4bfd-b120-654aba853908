/**
 * Example usage of the Gemini AI Service
 * 
 * This file contains examples of how to use the GeminiService for various
 * text and image generation tasks related to event management.
 */

import { geminiService } from './service';
import {
  generateEventDescriptionPrompt,
  generateInvitationMessagePrompt,
  generateEventInvitationImagePrompt,
  getOptimalAspectRatio
} from './utils';

/**
 * Example: Generate an event description
 */
export async function generateEventDescription() {
  try {
    const prompt = generateEventDescriptionPrompt({
      eventType: "Birthday Party",
      eventTitle: "<PERSON>'s 25th Birthday Celebration",
      eventDate: "Saturday, March 15th, 2024",
      eventLocation: "The Garden Terrace Restaurant",
      additionalDetails: "dinner, dancing, and live music"
    });

    const response = await geminiService.generateText({
      prompt,
      temperature: 0.8, // More creative
      maxOutputTokens: 500
    });

    console.log('Generated Event Description:', response.text);
    return response.text;
  } catch (error) {
    console.error('Error generating event description:', error);
    throw error;
  }
}

/**
 * Example: Generate a personalized invitation message
 */
export async function generatePersonalizedInvitation() {
  try {
    const prompt = generateInvitationMessagePrompt({
      guestName: "<PERSON>",
      eventTitle: "<PERSON>'s 25th Birthday Celebration",
      eventType: "birthday party",
      eventDate: "Saturday, March 15th at 7 PM",
      eventLocation: "The Garden Terrace Restaurant",
      personalNote: "your friendship means so much to Sarah"
    });

    const response = await geminiService.generateText({
      prompt,
      temperature: 0.7,
      maxOutputTokens: 300,
      systemInstruction: "You are a friendly event coordinator helping to create warm, personal invitation messages."
    });

    console.log('Generated Invitation:', response.text);
    return response.text;
  } catch (error) {
    console.error('Error generating invitation:', error);
    throw error;
  }
}

/**
 * Example: Generate an invitation image using Gemini native
 */
export async function generateInvitationImageWithGemini() {
  try {
    const prompt = generateEventInvitationImagePrompt({
      eventType: "birthday party",
      styleDescription: "modern and elegant",
      moodDescription: "joy and celebration",
      aestheticStyle: "minimalist with gold accents"
    });

    const result = await geminiService.generateImageWithGemini({
      prompt,
      includeText: true
    });

    console.log('Generated invitation image with Gemini:', {
      hasText: !!result.text,
      imageCount: result.images.length
    });

    return result;
  } catch (error) {
    console.error('Error generating invitation image with Gemini:', error);
    throw error;
  }
}

/**
 * Example: Generate high-quality images using Imagen 3
 */
export async function generateHighQualityImageWithImagen() {
  try {
    const prompt = "Elegant birthday party invitation background with gold and white theme, modern minimalist design, soft lighting, professional photography style";

    const images = await geminiService.generateImageWithImagen({
      prompt,
      numberOfImages: 2,
      aspectRatio: getOptimalAspectRatio('print_invitation'),
      personGeneration: "allow_adult"
    });

    console.log('Generated images with Imagen 3:', {
      imageCount: images.length
    });

    return images;
  } catch (error) {
    console.error('Error generating images with Imagen 3:', error);
    throw error;
  }
}

/**
 * Example: Chat-based event planning assistance
 */
export async function eventPlanningChat() {
  try {
    // Start a conversation about event planning
    const response1 = await geminiService.generateChatResponse({
      message: "I'm planning a birthday party for my friend. Can you help me brainstorm some unique theme ideas?",
      systemInstruction: "You are an experienced event planner who specializes in creative and memorable celebrations."
    });

    console.log('Event planner response 1:', response1.text);

    // Continue the conversation with more specific requirements
    const response2 = await geminiService.generateChatResponse({
      message: "Great ideas! I like the vintage theme. The party is for a 30-year-old who loves photography and travel. How can I incorporate these interests?",
      history: [
        {
          role: 'user',
          parts: [{ text: "I'm planning a birthday party for my friend. Can you help me brainstorm some unique theme ideas?" }]
        },
        {
          role: 'model',
          parts: [{ text: response1.text || "I'd be happy to help you plan a memorable birthday party!" }]
        }
      ],
      systemInstruction: "You are an experienced event planner who specializes in creative and memorable celebrations."
    });

    console.log('Event planner response 2:', response2.text);

    return { response1: response1.text, response2: response2.text };
  } catch (error) {
    console.error('Error in event planning chat:', error);
    throw error;
  }
}

/**
 * Example: Generate multiple content types for an event
 */
export async function generateCompleteEventContent() {
  try {
    const eventDetails = {
      eventType: "Wedding Reception",
      eventTitle: "Emma & James Wedding Reception",
      eventDate: "June 20th, 2024",
      eventLocation: "Sunset Gardens Venue",
      eventTime: "6:00 PM"
    };

    // Generate event description
    const descriptionPrompt = generateEventDescriptionPrompt({
      ...eventDetails,
      additionalDetails: "cocktail hour, dinner, dancing, and photo booth"
    });

    const description = await geminiService.generateText({
      prompt: descriptionPrompt,
      temperature: 0.8,
      maxOutputTokens: 400
    });

    // Generate invitation image
    const imagePrompt = generateEventInvitationImagePrompt({
      eventType: "wedding reception",
      styleDescription: "romantic and elegant",
      moodDescription: "love and celebration",
      aestheticStyle: "classic with floral elements"
    });

    const invitationImage = await geminiService.generateImageWithGemini({
      prompt: imagePrompt,
      includeText: false
    });

    // Generate social media images
    const socialImages = await geminiService.generateImageWithImagen({
      prompt: "Wedding reception announcement, elegant floral design, romantic atmosphere, Instagram post format",
      numberOfImages: 3,
      aspectRatio: "1:1"
    });

    console.log('Complete event content generated:', {
      description: description.text,
      invitationImageCount: invitationImage.images.length,
      socialImageCount: socialImages.length
    });

    return {
      description: description.text,
      invitationImages: invitationImage.images,
      socialImages
    };
  } catch (error) {
    console.error('Error generating complete event content:', error);
    throw error;
  }
}

/**
 * Example: Health check and service validation
 */
export async function validateGeminiService() {
  try {
    console.log('Checking Gemini service health...');
    
    const isHealthy = await geminiService.healthCheck();
    
    if (isHealthy) {
      console.log('✅ Gemini service is healthy and ready to use');
    } else {
      console.log('❌ Gemini service health check failed');
    }

    return isHealthy;
  } catch (error) {
    console.error('Error validating Gemini service:', error);
    return false;
  }
}

/**
 * Example: Streaming text generation for real-time responses
 */
export async function streamingTextGeneration() {
  try {
    console.log('Starting streaming text generation...');

    const prompt = "Write a detailed step-by-step guide for planning a successful corporate event, including timeline, budget considerations, and vendor management.";

    const stream = await geminiService.generateText({
      prompt,
      streaming: true,
      temperature: 0.7,
      maxOutputTokens: 1000
    });

    // Note: In a real application, you would handle the stream appropriately
    // This is just an example of how to initiate streaming
    console.log('Streaming response initiated');
    
    return stream;
  } catch (error) {
    console.error('Error in streaming text generation:', error);
    throw error;
  }
}
