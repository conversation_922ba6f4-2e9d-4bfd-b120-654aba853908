import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { Event, EventInvite, EventInviteListItem } from "@/types"
import { NextApiRequest } from "next"
import { debugLog } from "./logger"
import dayjs from "dayjs"
import { GetEventDateTimes, FormatDate } from "./dayjs"
import { Format24to12 } from "./time"
import { UTMParams, addUtmParams, generateQrUtmParams } from "./utm"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Utility function to handle focus management when transitioning from dropdown to modal
 * This prevents aria-hidden conflicts where a focused element becomes a descendant of an aria-hidden element
 * @param callback Function to execute after focus is properly managed
 * @param delay Delay in milliseconds before executing the callback (default: 50ms)
 */
export function handleDropdownToModalTransition(callback: () => void, delay: number = 50) {
  // Clear any existing focus to prevent aria-hidden conflicts
  if (document.activeElement instanceof HTMLElement) {
    document.activeElement.blur();
  }

  // Add a small delay to ensure dropdown closes before modal opens
  setTimeout(() => {
    callback();
  }, delay);
}

export function StringArrayToString(inp: string | number | string[]): string {
  if (Array.isArray(inp)) {
    return inp[0]
  }
  return inp.toString()
}

type InviteWithIds = Pick<EventInvite, 'eventId' | 'ID'> | Pick<EventInviteListItem, 'eventId' | 'ID'>

/**
 * Generate an RSVP link for an invite
 * @param invite Invite object with eventId and ID
 * @param utmParams Optional UTM parameters to add to the link
 * @returns RSVP link with optional UTM parameters
 */
export function generateRsvpLink(invite: InviteWithIds, utmParams?: UTMParams): string {
  const baseUrl = `${getBaseUrl()}/event/${invite.eventId}/rsvp/${invite.ID}`;

  // If UTM params are provided, add them to the URL
  if (utmParams) {
    return addUtmParams(baseUrl, utmParams);
  }

  return baseUrl;
}

/**
 * Generate a QR code RSVP link with UTM parameters
 * @param invite Invite object with eventId and ID
 * @param content Optional content identifier for utm_content
 * @returns RSVP link with QR-specific UTM parameters
 */
export function generateQrRsvpLink(invite: InviteWithIds, content?: string): string {
  const utmParams = generateQrUtmParams(invite.eventId, content);
  return generateRsvpLink(invite, utmParams);
}

/**
 * Generate a download link for an invite
 * @param invite Invite object with eventId and ID
 * @param printingPreferences Print settings for the invite
 * @param utmParams Optional UTM parameters to add to the link
 * @returns Download link with optional UTM parameters
 */
export function generateDownloadInviteLink(
  invite: InviteWithIds,
  printingPreferences: Event["printSettings"],
  utmParams?: UTMParams
): string {
  // Ensure invite has required properties
  if (!invite.ID || !invite.eventId) {
    throw new Error('Invite must have both ID and eventId properties');
  }

  // Use the media/export API endpoint for PDF downloads (same as the export functionality)
  const params = new URLSearchParams();
  params.append('type', 'invitation-cards-pdf');
  params.append('invites', invite.ID);

  const baseUrl = `${getBaseUrl()}/api/media/export?${params.toString()}`;

  // If UTM params are provided, add them to the URL
  if (utmParams) {
    return addUtmParams(baseUrl, utmParams);
  }

  return baseUrl;
}

export function getBaseUrl(req?: NextApiRequest): string {
  if (!req) {
    return 'https://iamcoming.io'
  }
  const host = req?.headers.host || 'iamcoming.io'
  const protocol = req?.headers['x-forwarded-proto'] || 'https'
  return `${protocol}://${host}`
}

type EnvVar =
  | 'NEXTAUTH_URL'
  | 'IAC_DEBUG'
  | 'STRIPE_SECRET_KEY'
  | 'STRIPE_WEBHOOK_SECRET'
  | 'EMAIL_SERVER_HOST'
  | 'EMAIL_SERVER_PORT'
  | 'EMAIL_SERVER_USER'
  | 'EMAIL_SERVER_PASSWORD'
  | 'EMAIL_FROM'
  | 'AUTH_SECRET'
  | 'FROM_EMAIL'
  | 'AWS_REGION'
  | 'AWS_ACCESS_KEY_ID'
  | 'AWS_SECRET_ACCESS_KEY'
  | 'FIREBASE_CLIENT_EMAIL'
  | 'FIREBASE_PRIVATE_KEY'
  | 'FIREBASE_PROJECT_ID'
  | 'GEMINI_API_KEY';

// Log all environment variables when this file is loaded
const envVars: EnvVar[] = [
  'NEXTAUTH_URL',
  'IAC_DEBUG',
  'STRIPE_SECRET_KEY',
  'STRIPE_WEBHOOK_SECRET',
  'EMAIL_SERVER_HOST',
  'EMAIL_SERVER_PORT',
  'EMAIL_SERVER_USER',
  'EMAIL_SERVER_PASSWORD',
  'EMAIL_FROM',
  'AUTH_SECRET',
  'FROM_EMAIL',
  'AWS_REGION',
  'AWS_ACCESS_KEY_ID',
  'AWS_SECRET_ACCESS_KEY',
  'FIREBASE_CLIENT_EMAIL',
  'FIREBASE_PRIVATE_KEY',
  'FIREBASE_PROJECT_ID',
  'GEMINI_API_KEY'
];

const envValues = envVars.reduce((acc, key) => {
  acc[key] = process.env[key] ? process.env[key] : 'undefined';
  return acc;
}, {} as Record<EnvVar, string>);

export function printenv() {
  debugLog('Environment Variables Status', envValues);
}

/**
 * Get an environment variable value
 * @param key The environment variable key
 * @param defaultValue Optional default value if the environment variable is not set
 * @returns The environment variable value or the default value
 */
export function envvar(key: EnvVar, defaultValue?: string): string {
  const value = process.env[key];

  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Environment variable ${key} is not set`);
  }

  return value;
}

/**
 * Generate an iCalendar (.ics) file content for an event
 * This creates a calendar attachment that can be automatically added to Gmail/Outlook calendars
 */
export function generateCalendarInvite(event: Event, invite?: EventInvite): string {
  // Get event timezone
  const eventTimezone = event.timezone || "Australia/Melbourne";

  // Format dates for iCalendar format (yyyyMMddTHHmmssZ)
  const eventDates = GetEventDateTimes(event);

  // Format dates for iCalendar in UTC as required by the spec
  const startDate = dayjs(eventDates.start).format('YYYYMMDDTHHmmss');
  const endDate = dayjs(eventDates.end).format('YYYYMMDDTHHmmss');

  // Current timestamp for created/last modified time
  const now = dayjs().format('YYYYMMDDTHHmmss');

  // Generate a unique ID for the event
  const uid = `${event.ID}@iamcoming.io`;

  // Format the description to match RSVP page layout, including timezone info
  const description = [
    `${event.eventName}`,
    `Date: ${FormatDate(event.eventDate, eventTimezone)}`,
    `Time: ${Format24to12(event.start)} - ${event.end ? Format24to12(event.end) : "TBD"}`,
    `Timezone: ${eventTimezone}`,
    `Location: ${event.location || ''}`,
    '',
    event.message || ''
  ].join('\\n');

  // Build the iCalendar content
  return [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//IamComing//Event Calendar//EN',
    'CALSCALE:GREGORIAN',
    'METHOD:REQUEST',
    'BEGIN:VEVENT',
    `UID:${uid}`,
    `DTSTAMP:${now}`,
    `DTSTART;TZID=${eventTimezone}:${startDate}`,
    `DTEND;TZID=${eventTimezone}:${endDate}`,
    `LOCATION:${event.location || ''}`,
    `SUMMARY:${event.eventName}`,
    `DESCRIPTION:${description}`,
    `ORGANIZER;CN=${event.host}:mailto:${event.ownerEmail}`,
    invite?.email ? `ATTENDEE;RSVP=TRUE;ROLE=REQ-PARTICIPANT;PARTSTAT=ACCEPTED;CN=${invite.name}:mailto:${invite.email}` : '',
    'BEGIN:VALARM',
    'ACTION:DISPLAY',
    'DESCRIPTION:Reminder',
    'TRIGGER:-P1D',
    'END:VALARM',
    'END:VEVENT',
    'END:VCALENDAR'
  ].filter(Boolean).join('\r\n');
}

/**
 * Generate a public URL for an image stored in Firebase Storage bucket
 * @param bucketName The name of the storage bucket (default: 'iamcoming-universe.firebasestorage.app')
 * @param filePath The path to the file within the bucket (e.g., 'event/123/invite.png')
 * @returns A public URL that can be used to access the image
 */
export function generateStorageImageUrl(
  filePath: string,
  bucketName: string = 'iamcoming-universe.firebasestorage.app'
): string {
  if (!filePath) {
    throw new Error('File path is required to generate storage URL');
  }

  // Ensure the path doesn't start with a slash
  const normalizedPath = filePath.startsWith('/') ? filePath.substring(1) : filePath;

  // Generate a public URL for Firebase Storage
  const publicUrl = `https://storage.googleapis.com/${bucketName}/${normalizedPath}`;

  return publicUrl;
}

export const mmToPixels = (mm: number, dpi: number) => Math.round((mm / 25.4) * dpi);

export function getInviteUrl(
  eventId: string,
  inviteId: string,
  utmParams?: UTMParams
): string {
  const baseUrl = `${getBaseUrl()}/event/${eventId}/rsvp/${inviteId}`;

  // If UTM params are provided, add them to the URL
  if (utmParams) {
    return addUtmParams(baseUrl, utmParams);
  }

  return baseUrl;
}

export function base64UrlEncode(str: string): string {
  return Buffer.from(str)
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}