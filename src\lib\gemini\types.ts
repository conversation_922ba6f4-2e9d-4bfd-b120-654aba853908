/**
 * Types for Google Gemini AI Service
 */

export interface TextGenerationOptions {
  prompt: string;
  model?: string;
  systemInstruction?: string;
  temperature?: number;
  maxOutputTokens?: number;
  streaming?: boolean;
}

export interface ChatMessage {
  role: 'user' | 'model';
  parts: Array<{ text: string }>;
}

export interface ChatGenerationOptions {
  message: string;
  history?: ChatMessage[];
  model?: string;
  systemInstruction?: string;
}

export interface GeminiImageGenerationOptions {
  prompt: string;
  model?: string;
  includeText?: boolean;
}

export interface ImageEditingOptions {
  prompt: string;
  imageData: Buffer;
  mimeType: string;
  model?: string;
}

export interface ImagenGenerationOptions {
  prompt: string;
  numberOfImages?: number;
  aspectRatio?: "1:1" | "3:4" | "4:3" | "9:16" | "16:9";
  personGeneration?: "dont_allow" | "allow_adult" | "allow_all";
  model?: string;
}

export interface GeneratedContent {
  text: string | null;
  images: Buffer[];
}

export interface GeminiModels {
  // Text Generation Models
  TEXT_GEMINI_2_0_FLASH: "gemini-2.0-flash";
  TEXT_GEMINI_2_5_FLASH_PREVIEW: "gemini-2.5-flash-preview";
  TEXT_GEMINI_2_5_PRO_PREVIEW: "gemini-2.5-pro-preview";
  
  // Image Generation Models
  IMAGE_GEMINI_2_0_FLASH_PREVIEW: "gemini-2.0-flash-preview-image-generation";
  IMAGE_IMAGEN_3: "imagen-3.0-generate-002";
}

export const GEMINI_MODELS: GeminiModels = {
  TEXT_GEMINI_2_0_FLASH: "gemini-2.0-flash",
  TEXT_GEMINI_2_5_FLASH_PREVIEW: "gemini-2.5-flash-preview",
  TEXT_GEMINI_2_5_PRO_PREVIEW: "gemini-2.5-pro-preview",
  IMAGE_GEMINI_2_0_FLASH_PREVIEW: "gemini-2.0-flash-preview-image-generation",
  IMAGE_IMAGEN_3: "imagen-3.0-generate-002",
};

export interface GeminiServiceConfig {
  apiKey: string;
  defaultTextModel?: string;
  defaultImageModel?: string;
  defaultTemperature?: number;
  defaultMaxTokens?: number;
}

export interface GeminiError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
}

// Prompt templates for common use cases
export interface PromptTemplate {
  name: string;
  description: string;
  template: string;
  variables: string[];
}

export const PROMPT_TEMPLATES: PromptTemplate[] = [
  {
    name: "event_description",
    description: "Generate engaging event descriptions",
    template: "Create an engaging and informative description for a {eventType} event titled '{eventTitle}'. The event will take place on {eventDate} at {eventLocation}. Include details about {additionalDetails}. Make it sound exciting and encourage attendance.",
    variables: ["eventType", "eventTitle", "eventDate", "eventLocation", "additionalDetails"]
  },
  {
    name: "invitation_message",
    description: "Generate personalized invitation messages",
    template: "Write a warm and personalized invitation message for {guestName} to attend {eventTitle}. The event is a {eventType} happening on {eventDate} at {eventLocation}. Mention {personalNote} and make them feel special about being invited.",
    variables: ["guestName", "eventTitle", "eventType", "eventDate", "eventLocation", "personalNote"]
  },
  {
    name: "event_reminder",
    description: "Generate event reminder messages",
    template: "Create a friendly reminder message for {eventTitle} happening {timeUntilEvent}. Include key details like location ({eventLocation}), time ({eventTime}), and any important notes: {importantNotes}. Keep it concise but informative.",
    variables: ["eventTitle", "timeUntilEvent", "eventLocation", "eventTime", "importantNotes"]
  },
  {
    name: "thank_you_message",
    description: "Generate thank you messages after events",
    template: "Write a heartfelt thank you message to attendees of {eventTitle}. Express gratitude for their participation and mention {eventHighlights}. Include any follow-up information: {followUpInfo}.",
    variables: ["eventTitle", "eventHighlights", "followUpInfo"]
  }
];

// Image generation prompt templates
export const IMAGE_PROMPT_TEMPLATES: PromptTemplate[] = [
  {
    name: "event_invitation_image",
    description: "Generate invitation images for events",
    template: "Create an elegant and eye-catching invitation image for a {eventType} event. The design should be {styleDescription} with colors that convey {moodDescription}. Include space for text overlay. The overall aesthetic should be {aestheticStyle}.",
    variables: ["eventType", "styleDescription", "moodDescription", "aestheticStyle"]
  },
  {
    name: "event_background",
    description: "Generate background images for events",
    template: "Design a beautiful background image suitable for a {eventType} event. The image should have a {colorScheme} color scheme and {atmosphereDescription} atmosphere. Style: {designStyle}. Ensure the image works well as a background with text overlay.",
    variables: ["eventType", "colorScheme", "atmosphereDescription", "designStyle"]
  },
  {
    name: "social_media_post",
    description: "Generate images for social media promotion",
    template: "Create a vibrant and engaging social media image to promote a {eventType} event. The design should be {designStyle} with {colorPalette} colors. Include visual elements that represent {eventTheme}. Optimized for {platform} format.",
    variables: ["eventType", "designStyle", "colorPalette", "eventTheme", "platform"]
  }
];

export interface TemplateVariable {
  name: string;
  description: string;
  required: boolean;
  defaultValue?: string;
}

export interface ProcessedTemplate {
  content: string;
  missingVariables: string[];
}
