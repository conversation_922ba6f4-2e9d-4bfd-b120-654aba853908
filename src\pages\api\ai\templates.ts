import { NextApiRequest, NextApiResponse } from 'next';
import { 
  getPromptTemplates, 
  getImagePromptTemplates, 
  processPromptTemplate,
  generateEventDescriptionPrompt,
  generateInvitationMessagePrompt,
  generateEventReminderPrompt,
  generateThankYouMessagePrompt
} from '@/lib/gemini';
import { debugLog } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    // Return all available templates
    try {
      const textTemplates = getPromptTemplates();
      const imageTemplates = getImagePromptTemplates();

      return res.status(200).json({
        success: true,
        data: {
          textTemplates,
          imageTemplates,
          quickExamples: {
            text: [
              {
                name: "Event Description",
                prompt: "Write an engaging description for a birthday party event happening this Saturday at Central Park. Include details about food, activities, and what guests should bring.",
                systemInstruction: "You are an experienced event coordinator who creates compelling event descriptions."
              },
              {
                name: "Invitation Message",
                prompt: "Create a personalized invitation message for <PERSON>'s wedding reception. Make it warm and elegant.",
                systemInstruction: "You are a professional event planner who writes beautiful, personalized invitations."
              },
              {
                name: "Event Reminder",
                prompt: "Generate a friendly reminder message for a corporate networking event happening tomorrow at 6 PM at the Downtown Conference Center.",
                systemInstruction: "You are a helpful assistant who creates clear and professional event reminders."
              },
              {
                name: "Thank You Message",
                prompt: "Generate a thank you message for attendees of a charity fundraising gala. Express gratitude and mention the successful fundraising goal achievement.",
                systemInstruction: "You are writing on behalf of a nonprofit organization, expressing genuine gratitude."
              }
            ],
            image: [
              {
                name: "Birthday Invitation",
                prompt: "Elegant birthday party invitation with gold accents, modern minimalist design, soft lighting, professional photography style"
              },
              {
                name: "Wedding Background",
                prompt: "Wedding reception background with romantic floral elements, soft pink and white color scheme, dreamy atmosphere"
              },
              {
                name: "Corporate Event",
                prompt: "Corporate event poster design, professional blue and white theme, modern business aesthetic, clean typography space"
              },
              {
                name: "Social Media Post",
                prompt: "Instagram post for music festival announcement, vibrant colors, energetic atmosphere, festival vibes, square format"
              }
            ]
          }
        }
      });
    } catch (error) {
      debugLog('Error fetching templates', { error });
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch templates'
      });
    }
  }

  if (req.method === 'POST') {
    // Process a template with variables
    try {
      const { templateName, variables, isImageTemplate } = req.body;

      if (!templateName) {
        return res.status(400).json({
          success: false,
          error: 'Template name is required'
        });
      }

      const result = processPromptTemplate(templateName, variables || {}, isImageTemplate);

      return res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      debugLog('Error processing template', { error });
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return res.status(400).json({
        success: false,
        error: errorMessage
      });
    }
  }

  return res.status(405).json({ error: 'Method not allowed' });
}
