import { GoogleGenAI , HarmCategory, HarmBlockThreshold, Modality } from "@google/genai";

const API_KEY = process.env.GEMINI_API_KEY // Ensure you have GEMINI_API_KEY in your .env file
const genAI = new GoogleGenAI (API_KEY);

const generationConfig = {
    temperature: 0.9,
    topK: 1,
    topP: 1,
    maxOutputTokens: 2048,
};

const safetySettings = [
    {
        category: HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
        category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
        category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
        category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
];

export async function generateDescription(prompt: string): Promise<string> {
    try {
        const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash", generationConfig, safetySettings });
        const result = await model.generateContent(prompt);
        const response = result.response;
        return response.text();
    } catch (error) {
        console.error("Error generating description:", error);
        throw new Error("Failed to generate description");
    }
}

export async function generateImage(prompt: string): Promise<string> {
    try {
        const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-preview-image-generation", generationConfig, safetySettings });
        const result = await model.generateContent({
            contents: [{ role: "user", parts: [{ text: prompt }] }],
            config: {
                responseModalities: [Modality.IMAGE],
            },
        });
        const response = result.response;
        // Assuming the image is returned as base64 data in the first part
        if (response.candidates && response.candidates[0].content.parts[0].inlineData) {
            return response.candidates[0].content.parts[0].inlineData.data;
        }
        throw new Error("No image data found in response");

    } catch (error) {
        console.error("Error generating image:", error);
        throw new Error("Failed to generate image");
    }
}

export async function generateImageWithTextEdit(prompt: string, imageBase64: string, imageMimeType: string): Promise<string> {
    try {
        const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-preview-image-generation", generationConfig, safetySettings });
        const result = await model.generateContent({
            contents: [
                { role: "user", parts: [{ text: prompt }, { inlineData: { mimeType: imageMimeType, data: imageBase64 } }] }
            ],
            config: {
                responseModalities: [Modality.IMAGE],
            },
        });
        const response = result.response;
        if (response.candidates && response.candidates[0].content.parts[0].inlineData) {
            return response.candidates[0].content.parts[0].inlineData.data;
        }
        throw new Error("No image data found in response");
    } catch (error) {
        console.error("Error generating image with text edit:", error);
        throw new Error("Failed to generate image with text edit");
    }
}

// Example of how to use the image generation and then save it (server-side)
// import fs from 'fs';
// async function exampleUsage() {
//   try {
//     const imagePrompt = "A futuristic cityscape with flying cars";
//     const base64ImageData = await generateImage(imagePrompt);
//     fs.writeFileSync("generated_image.png", Buffer.from(base64ImageData, 'base64'));
//     console.log("Image saved as generated_image.png");

//     const descriptionPrompt = "Describe a peaceful beach scene at sunset.";
//     const description = await generateDescription(descriptionPrompt);
//     console.log("Generated Description:", description);
//   } catch (error) {
//     console.error("Example usage failed:", error);
//   }
// }
// exampleUsage();
