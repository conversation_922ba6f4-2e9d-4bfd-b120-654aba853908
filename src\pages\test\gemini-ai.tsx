import { useState, useEffect } from 'react';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Loader2, CheckCircle, XCircle, Download, Copy } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import Image from 'next/image';

interface HealthStatus {
  status: 'checking' | 'healthy' | 'unhealthy' | 'error';
  message: string;
  timestamp?: string;
}

interface GenerationResult {
  text?: string;
  images?: string[];
  imageCount?: number;
  model?: string;
  metadata?: any;
}

export default function GeminiAITestPage() {
  const { toast } = useToast();
  const [healthStatus, setHealthStatus] = useState<HealthStatus>({ status: 'checking', message: 'Checking...' });
  
  // Text generation state
  const [textPrompt, setTextPrompt] = useState('');
  const [textModel, setTextModel] = useState('gemini-2.0-flash');
  const [systemInstruction, setSystemInstruction] = useState('');
  const [temperature, setTemperature] = useState(0.7);
  const [maxTokens, setMaxTokens] = useState(1000);
  const [textLoading, setTextLoading] = useState(false);
  const [textResult, setTextResult] = useState<GenerationResult | null>(null);

  // Image generation state
  const [imagePrompt, setImagePrompt] = useState('');
  const [imageModel, setImageModel] = useState('gemini');
  const [aspectRatio, setAspectRatio] = useState('1:1');
  const [numberOfImages, setNumberOfImages] = useState(1);
  const [includeText, setIncludeText] = useState(true);
  const [imageLoading, setImageLoading] = useState(false);
  const [imageResult, setImageResult] = useState<GenerationResult | null>(null);

  // Health check on component mount
  useEffect(() => {
    checkHealth();
  }, []);

  const checkHealth = async () => {
    setHealthStatus({ status: 'checking', message: 'Checking service health...' });
    
    try {
      const response = await fetch('/api/ai/health-check');
      const data = await response.json();
      
      if (data.success) {
        setHealthStatus({
          status: 'healthy',
          message: data.message,
          timestamp: data.timestamp
        });
      } else {
        setHealthStatus({
          status: 'unhealthy',
          message: data.message || 'Service is not healthy',
          timestamp: data.timestamp
        });
      }
    } catch (error) {
      setHealthStatus({
        status: 'error',
        message: 'Failed to check service health',
        timestamp: new Date().toISOString()
      });
    }
  };

  const generateText = async () => {
    if (!textPrompt.trim()) {
      toast({
        title: "Error",
        description: "Please enter a text prompt",
        variant: "destructive"
      });
      return;
    }

    setTextLoading(true);
    setTextResult(null);

    try {
      const response = await fetch('/api/ai/generate-text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: textPrompt,
          model: textModel,
          systemInstruction: systemInstruction || undefined,
          temperature,
          maxOutputTokens: maxTokens
        }),
      });

      const data = await response.json();

      if (data.success) {
        setTextResult(data);
        toast({
          title: "Success",
          description: "Text generated successfully!",
        });
      } else {
        throw new Error(data.details || data.error || 'Failed to generate text');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setTextLoading(false);
    }
  };

  const generateImage = async () => {
    if (!imagePrompt.trim()) {
      toast({
        title: "Error",
        description: "Please enter an image prompt",
        variant: "destructive"
      });
      return;
    }

    setImageLoading(true);
    setImageResult(null);

    try {
      const response = await fetch('/api/ai/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: imagePrompt,
          useImagen: imageModel === 'imagen',
          aspectRatio,
          numberOfImages: imageModel === 'imagen' ? numberOfImages : 1,
          includeText: imageModel === 'gemini' ? includeText : false
        }),
      });

      const data = await response.json();

      if (data.success) {
        setImageResult(data);
        toast({
          title: "Success",
          description: `Generated ${data.imageCount} image(s) successfully!`,
        });
      } else {
        throw new Error(data.details || data.error || 'Failed to generate image');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setImageLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "Text copied to clipboard",
    });
  };

  const downloadImage = (base64: string, index: number) => {
    const link = document.createElement('a');
    link.href = `data:image/png;base64,${base64}`;
    link.download = `generated-image-${index + 1}.png`;
    link.click();
  };

  const getHealthStatusIcon = () => {
    switch (healthStatus.status) {
      case 'checking':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'unhealthy':
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getHealthStatusColor = () => {
    switch (healthStatus.status) {
      case 'checking':
        return 'bg-yellow-100 text-yellow-800';
      case 'healthy':
        return 'bg-green-100 text-green-800';
      case 'unhealthy':
      case 'error':
        return 'bg-red-100 text-red-800';
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header buttons={[]} />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">Gemini AI Service Test</h1>
            <p className="text-muted-foreground">
              Test the Google Gemini AI service for text and image generation
            </p>
          </div>

          {/* Health Status */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Service Health Status
                {getHealthStatusIcon()}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge className={getHealthStatusColor()}>
                    {healthStatus.status.toUpperCase()}
                  </Badge>
                  <span>{healthStatus.message}</span>
                </div>
                <Button variant="outline" size="sm" onClick={checkHealth}>
                  Refresh
                </Button>
              </div>
              {healthStatus.timestamp && (
                <p className="text-sm text-muted-foreground mt-2">
                  Last checked: {new Date(healthStatus.timestamp).toLocaleString()}
                </p>
              )}
            </CardContent>
          </Card>

          {/* Main Content */}
          <Tabs defaultValue="text" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="text">Text Generation</TabsTrigger>
              <TabsTrigger value="image">Image Generation</TabsTrigger>
            </TabsList>

            {/* Text Generation Tab */}
            <TabsContent value="text" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Text Input */}
                <Card>
                  <CardHeader>
                    <CardTitle>Text Generation</CardTitle>
                    <CardDescription>
                      Generate text content using Gemini models
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="text-prompt">Prompt</Label>
                      <Textarea
                        id="text-prompt"
                        placeholder="Enter your text prompt here..."
                        value={textPrompt}
                        onChange={(e) => setTextPrompt(e.target.value)}
                        rows={4}
                      />
                    </div>

                    <div>
                      <Label htmlFor="system-instruction">System Instruction (Optional)</Label>
                      <Textarea
                        id="system-instruction"
                        placeholder="You are a helpful assistant..."
                        value={systemInstruction}
                        onChange={(e) => setSystemInstruction(e.target.value)}
                        rows={2}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="text-model">Model</Label>
                        <Select value={textModel} onValueChange={setTextModel}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="gemini-2.0-flash">Gemini 2.0 Flash</SelectItem>
                            <SelectItem value="gemini-2.5-flash-preview">Gemini 2.5 Flash Preview</SelectItem>
                            <SelectItem value="gemini-2.5-pro-preview">Gemini 2.5 Pro Preview</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="temperature">Temperature</Label>
                        <Input
                          id="temperature"
                          type="number"
                          min="0"
                          max="1"
                          step="0.1"
                          value={temperature}
                          onChange={(e) => setTemperature(parseFloat(e.target.value))}
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="max-tokens">Max Tokens</Label>
                      <Input
                        id="max-tokens"
                        type="number"
                        min="1"
                        max="8000"
                        value={maxTokens}
                        onChange={(e) => setMaxTokens(parseInt(e.target.value))}
                      />
                    </div>

                    <Button 
                      onClick={generateText} 
                      disabled={textLoading || healthStatus.status !== 'healthy'}
                      className="w-full"
                    >
                      {textLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        'Generate Text'
                      )}
                    </Button>
                  </CardContent>
                </Card>

                {/* Text Result */}
                <Card>
                  <CardHeader>
                    <CardTitle>Generated Text</CardTitle>
                    <CardDescription>
                      The generated text will appear here
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {textResult ? (
                      <div className="space-y-4">
                        <div className="relative">
                          <Textarea
                            value={textResult.text || ''}
                            readOnly
                            rows={10}
                            className="resize-none"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            className="absolute top-2 right-2"
                            onClick={() => copyToClipboard(textResult.text || '')}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                        
                        <div className="text-sm text-muted-foreground">
                          <p>Model: {textResult.model}</p>
                          <p>Length: {textResult.text?.length || 0} characters</p>
                          {textResult.metadata?.timestamp && (
                            <p>Generated: {new Date(textResult.metadata.timestamp).toLocaleString()}</p>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center text-muted-foreground py-8">
                        No text generated yet. Enter a prompt and click "Generate Text" to start.
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Image Generation Tab */}
            <TabsContent value="image" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Image Input */}
                <Card>
                  <CardHeader>
                    <CardTitle>Image Generation</CardTitle>
                    <CardDescription>
                      Generate images using Gemini or Imagen models
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="image-prompt">Prompt</Label>
                      <Textarea
                        id="image-prompt"
                        placeholder="Describe the image you want to generate..."
                        value={imagePrompt}
                        onChange={(e) => setImagePrompt(e.target.value)}
                        rows={4}
                      />
                    </div>

                    <div>
                      <Label htmlFor="image-model">Model</Label>
                      <Select value={imageModel} onValueChange={setImageModel}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="gemini">Gemini Native (Contextual)</SelectItem>
                          <SelectItem value="imagen">Imagen 3 (High Quality)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="aspect-ratio">Aspect Ratio</Label>
                        <Select value={aspectRatio} onValueChange={setAspectRatio}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1:1">Square (1:1)</SelectItem>
                            <SelectItem value="3:4">Portrait (3:4)</SelectItem>
                            <SelectItem value="4:3">Landscape (4:3)</SelectItem>
                            <SelectItem value="9:16">Mobile (9:16)</SelectItem>
                            <SelectItem value="16:9">Widescreen (16:9)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {imageModel === 'imagen' && (
                        <div>
                          <Label htmlFor="number-of-images">Number of Images</Label>
                          <Select value={numberOfImages.toString()} onValueChange={(value) => setNumberOfImages(parseInt(value))}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1">1 Image</SelectItem>
                              <SelectItem value="2">2 Images</SelectItem>
                              <SelectItem value="3">3 Images</SelectItem>
                              <SelectItem value="4">4 Images</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}

                      {imageModel === 'gemini' && (
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id="include-text"
                            checked={includeText}
                            onChange={(e) => setIncludeText(e.target.checked)}
                          />
                          <Label htmlFor="include-text">Include text response</Label>
                        </div>
                      )}
                    </div>

                    <Button
                      onClick={generateImage}
                      disabled={imageLoading || healthStatus.status !== 'healthy'}
                      className="w-full"
                    >
                      {imageLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        'Generate Image'
                      )}
                    </Button>
                  </CardContent>
                </Card>

                {/* Image Result */}
                <Card>
                  <CardHeader>
                    <CardTitle>Generated Images</CardTitle>
                    <CardDescription>
                      The generated images will appear here
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {imageResult ? (
                      <div className="space-y-4">
                        {/* Text response if available */}
                        {imageResult.text && (
                          <div>
                            <Label>AI Response:</Label>
                            <div className="relative">
                              <Textarea
                                value={imageResult.text}
                                readOnly
                                rows={3}
                                className="resize-none"
                              />
                              <Button
                                variant="outline"
                                size="sm"
                                className="absolute top-2 right-2"
                                onClick={() => copyToClipboard(imageResult.text || '')}
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        )}

                        {/* Generated Images */}
                        {imageResult.images && imageResult.images.length > 0 && (
                          <div>
                            <Label>Generated Images:</Label>
                            <div className="grid grid-cols-1 gap-4 mt-2">
                              {imageResult.images.map((base64Image, index) => (
                                <div key={index} className="relative border rounded-lg overflow-hidden">
                                  <Image
                                    src={`data:image/png;base64,${base64Image}`}
                                    alt={`Generated image ${index + 1}`}
                                    width={400}
                                    height={400}
                                    className="w-full h-auto"
                                  />
                                  <div className="absolute top-2 right-2 flex gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => downloadImage(base64Image, index)}
                                    >
                                      <Download className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        <Separator />

                        <div className="text-sm text-muted-foreground">
                          <p>Model: {imageResult.model}</p>
                          <p>Images: {imageResult.imageCount || 0}</p>
                          {imageResult.metadata?.timestamp && (
                            <p>Generated: {new Date(imageResult.metadata.timestamp).toLocaleString()}</p>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center text-muted-foreground py-8">
                        No images generated yet. Enter a prompt and click "Generate Image" to start.
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>

          {/* Quick Examples */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Quick Examples</CardTitle>
              <CardDescription>
                Try these example prompts to get started
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-2">Text Generation Examples:</h4>
                  <div className="space-y-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start text-left"
                      onClick={() => setTextPrompt("Write an engaging description for a birthday party event happening this Saturday at Central Park. Include details about food, activities, and what guests should bring.")}
                    >
                      Event Description
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start text-left"
                      onClick={() => setTextPrompt("Create a personalized invitation message for Sarah's wedding reception. Make it warm and elegant.")}
                    >
                      Invitation Message
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start text-left"
                      onClick={() => setTextPrompt("Generate a thank you message for attendees of a corporate networking event. Express gratitude and mention the key highlights.")}
                    >
                      Thank You Message
                    </Button>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Image Generation Examples:</h4>
                  <div className="space-y-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start text-left"
                      onClick={() => setImagePrompt("Elegant birthday party invitation with gold accents, modern minimalist design, soft lighting")}
                    >
                      Birthday Invitation
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start text-left"
                      onClick={() => setImagePrompt("Wedding reception background with romantic floral elements, soft pink and white color scheme")}
                    >
                      Wedding Background
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start text-left"
                      onClick={() => setImagePrompt("Corporate event poster design, professional blue and white theme, modern business aesthetic")}
                    >
                      Corporate Event
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>

      <Footer type="app" />
    </div>
  );
}
