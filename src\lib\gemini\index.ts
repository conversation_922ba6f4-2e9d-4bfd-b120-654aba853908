/**
 * Google Gemini AI Service Module
 * 
 * This module provides comprehensive AI capabilities for text and image generation
 * using Google's Gemini API. It includes both Gemini native image generation
 * and Imagen 3 for specialized high-quality image generation.
 */

// Export the main service
export { GeminiService, geminiService } from './service';

// Export types
export type {
  TextGenerationOptions,
  ChatMessage,
  ChatGenerationOptions,
  GeminiImageGenerationOptions,
  ImageEditingOptions,
  ImagenGenerationOptions,
  GeneratedContent,
  GeminiModels,
  GeminiServiceConfig,
  GeminiError,
  PromptTemplate,
  TemplateVariable,
  ProcessedTemplate
} from './types';

// Export constants
export { GEMINI_MODELS, PROMPT_TEMPLATES, IMAGE_PROMPT_TEMPLATES } from './types';

// Export utility functions
export {
  processPromptTemplate,
  getPromptTemplates,
  getImagePromptTemplates,
  getTemplate,
  validateTemplateVariables,
  generateEventDescriptionPrompt,
  generateInvitationMessagePrompt,
  generateEventReminderPrompt,
  generateThankYouMessagePrompt,
  generateEventInvitationImagePrompt,
  generateEventBackgroundImagePrompt,
  generateSocialMediaImagePrompt,
  sanitizePrompt,
  getOptimalAspectRatio,
  estimateTokenCount,
  isPromptWithinLimits
} from './utils';

// Re-export commonly used types from @google/genai for convenience
export { Modality } from '@google/genai';
