# Gemini AI Service

This module provides comprehensive AI capabilities for text and image generation using Google's Gemini API. It's specifically designed for event management applications and includes both Gemini native image generation and Imagen 3 for specialized high-quality image generation.

## Features

### Text Generation
- **Event Descriptions**: Generate engaging event descriptions
- **Invitation Messages**: Create personalized invitation messages
- **Event Reminders**: Generate reminder messages
- **Thank You Messages**: Create post-event thank you messages
- **Chat-based Assistance**: Multi-turn conversations for event planning
- **Streaming Responses**: Real-time text generation

### Image Generation
- **Gemini Native**: Contextual image generation and editing
- **Imagen 3**: High-quality, artistic image generation
- **Multiple Formats**: Support for various aspect ratios
- **Template-based**: Pre-built prompts for common use cases

## Quick Start

### Basic Setup

```typescript
import { geminiService } from '@/lib/gemini';

// The service is automatically initialized with your GEMINI_API_KEY
```

### Generate Event Description

```typescript
import { geminiService, generateEventDescriptionPrompt } from '@/lib/gemini';

const prompt = generateEventDescriptionPrompt({
  eventType: "Birthday Party",
  eventTitle: "Sarah's 25th Birthday",
  eventDate: "March 15th, 2024",
  eventLocation: "Garden Terrace Restaurant",
  additionalDetails: "dinner, dancing, and live music"
});

const response = await geminiService.generateText({
  prompt,
  temperature: 0.8,
  maxOutputTokens: 500
});

console.log(response.text);
```

### Generate Invitation Image

```typescript
// Using Gemini Native (contextual, conversational)
const result = await geminiService.generateImageWithGemini({
  prompt: "Create an elegant birthday party invitation with gold accents",
  includeText: true
});

// Using Imagen 3 (high-quality, artistic)
const images = await geminiService.generateImageWithImagen({
  prompt: "Elegant birthday party invitation, modern design, gold and white theme",
  numberOfImages: 2,
  aspectRatio: "3:4"
});
```

### Chat-based Event Planning

```typescript
const response = await geminiService.generateChatResponse({
  message: "Help me plan a unique birthday party theme",
  systemInstruction: "You are an experienced event planner"
});

// Continue the conversation
const followUp = await geminiService.generateChatResponse({
  message: "I like the vintage theme. How can I incorporate photography?",
  history: [
    { role: 'user', parts: [{ text: "Help me plan a unique birthday party theme" }] },
    { role: 'model', parts: [{ text: response.text }] }
  ]
});
```

## Available Templates

### Text Templates
- `event_description` - Generate engaging event descriptions
- `invitation_message` - Create personalized invitations
- `event_reminder` - Generate reminder messages
- `thank_you_message` - Post-event thank you messages

### Image Templates
- `event_invitation_image` - Invitation image generation
- `event_background` - Background images for events
- `social_media_post` - Social media promotional images

## API Reference

### GeminiService Methods

#### `generateText(options)`
Generate text content using Gemini models.

**Parameters:**
- `prompt` (string): The text prompt
- `model` (string, optional): Model to use (default: "gemini-2.0-flash")
- `systemInstruction` (string, optional): System instruction for behavior
- `temperature` (number, optional): Creativity level (0-1, default: 0.7)
- `maxOutputTokens` (number, optional): Maximum response length (default: 1000)
- `streaming` (boolean, optional): Enable streaming responses (default: false)

#### `generateImageWithGemini(options)`
Generate images using Gemini native capabilities.

**Parameters:**
- `prompt` (string): The image description
- `model` (string, optional): Model to use
- `includeText` (boolean, optional): Include text response (default: true)

#### `generateImageWithImagen(options)`
Generate high-quality images using Imagen 3.

**Parameters:**
- `prompt` (string): The image description
- `numberOfImages` (number, optional): Number of images (1-4, default: 1)
- `aspectRatio` (string, optional): Image ratio ("1:1", "3:4", "4:3", "9:16", "16:9")
- `personGeneration` (string, optional): Person generation policy

#### `editImageWithGemini(options)`
Edit existing images using Gemini.

**Parameters:**
- `prompt` (string): Edit instruction
- `imageData` (Buffer): Original image data
- `mimeType` (string): Image MIME type
- `model` (string, optional): Model to use

#### `generateChatResponse(options)`
Generate responses in a conversational context.

**Parameters:**
- `message` (string): User message
- `history` (array, optional): Conversation history
- `model` (string, optional): Model to use
- `systemInstruction` (string, optional): System instruction

#### `healthCheck()`
Check if the service is working properly.

**Returns:** `Promise<boolean>`

## Utility Functions

### Template Processing
```typescript
import { processPromptTemplate, validateTemplateVariables } from '@/lib/gemini';

// Process a template
const result = processPromptTemplate('event_description', {
  eventType: 'Birthday Party',
  eventTitle: 'Sarah\'s Birthday',
  // ... other variables
});

// Validate template variables
const validation = validateTemplateVariables('event_description', variables);
if (!validation.isValid) {
  console.log('Missing variables:', validation.missingVariables);
}
```

### Helper Functions
```typescript
import { 
  getOptimalAspectRatio, 
  estimateTokenCount, 
  sanitizePrompt 
} from '@/lib/gemini';

// Get optimal aspect ratio for use case
const ratio = getOptimalAspectRatio('instagram_post'); // "1:1"

// Estimate token count
const tokens = estimateTokenCount(prompt);

// Sanitize user input
const cleanPrompt = sanitizePrompt(userInput);
```

## Environment Setup

Make sure you have the `GEMINI_API_KEY` environment variable set:

```bash
# .env.local
GEMINI_API_KEY=your_api_key_here
```

Get your API key from [Google AI Studio](https://aistudio.google.com/apikey).

## Error Handling

```typescript
try {
  const response = await geminiService.generateText({ prompt: "Hello" });
  console.log(response.text);
} catch (error) {
  if (error.message.includes('API key')) {
    console.error('Invalid API key');
  } else if (error.message.includes('rate limit')) {
    console.error('Rate limit exceeded');
  } else {
    console.error('Generation failed:', error.message);
  }
}
```

## Best Practices

1. **Use appropriate models**: Gemini for contextual content, Imagen 3 for high-quality images
2. **Set reasonable token limits**: Avoid excessive generation costs
3. **Implement proper error handling**: Handle API failures gracefully
4. **Use templates**: Leverage pre-built templates for consistency
5. **Sanitize inputs**: Always sanitize user-provided prompts
6. **Cache results**: Consider caching generated content when appropriate

## Examples

See `examples.ts` for comprehensive usage examples including:
- Event description generation
- Personalized invitations
- Image generation workflows
- Chat-based event planning
- Complete event content generation

## Models Available

### Text Generation
- `gemini-2.0-flash` - Latest multimodal model (default)
- `gemini-2.5-flash-preview` - Experimental features
- `gemini-2.5-pro-preview` - Most powerful reasoning

### Image Generation
- `gemini-2.0-flash-preview-image-generation` - Native image generation
- `imagen-3.0-generate-002` - Specialized high-quality images
