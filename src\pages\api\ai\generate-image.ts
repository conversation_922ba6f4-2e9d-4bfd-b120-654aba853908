import { NextApiRequest, NextApiResponse } from 'next';
import { geminiService } from '@/lib/gemini';
import { debugLog } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      prompt,
      model,
      includeText,
      numberOfImages,
      aspectRatio,
      personGeneration,
      useImagen
    } = req.body;

    // Validate required fields
    if (!prompt || typeof prompt !== 'string') {
      return res.status(400).json({ error: 'Prompt is required and must be a string' });
    }

    debugLog('AI Image Generation Request', {
      promptLength: prompt.length,
      useImagen: useImagen || false,
      numberOfImages: numberOfImages || 1,
      aspectRatio: aspectRatio || 'default'
    });

    let result;

    if (useImagen) {
      // Use Imagen 3 for high-quality image generation
      const images = await geminiService.generateImageWithImagen({
        prompt,
        numberOfImages: numberOfImages || 1,
        aspectRatio: aspectRatio || "1:1",
        personGeneration: personGeneration || "allow_adult",
        model: model || "imagen-3.0-generate-002"
      });

      // Convert images to base64 for JSON response
      const base64Images = images.map(buffer => buffer.toString('base64'));

      result = {
        text: null,
        images: base64Images,
        imageCount: images.length
      };
    } else {
      // Use Gemini native image generation
      const response = await geminiService.generateImageWithGemini({
        prompt,
        model: model || "gemini-2.0-flash-preview-image-generation",
        includeText: includeText !== false
      });

      // Convert images to base64 for JSON response
      const base64Images = response.images.map(buffer => buffer.toString('base64'));

      result = {
        text: response.text,
        images: base64Images,
        imageCount: response.images.length
      };
    }

    debugLog('AI Image Generation Success', {
      hasText: !!result.text,
      imageCount: result.imageCount,
      useImagen: useImagen || false
    });

    return res.status(200).json({
      success: true,
      ...result,
      model: useImagen ? (model || "imagen-3.0-generate-002") : (model || "gemini-2.0-flash-preview-image-generation"),
      metadata: {
        promptLength: prompt.length,
        useImagen: useImagen || false,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    debugLog('AI Image Generation Error', { error });
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    // Handle specific error types
    if (errorMessage.includes('API key')) {
      return res.status(401).json({ 
        error: 'Invalid API key or authentication failed',
        details: 'Please check your Gemini API key configuration'
      });
    }
    
    if (errorMessage.includes('rate limit')) {
      return res.status(429).json({ 
        error: 'Rate limit exceeded',
        details: 'Please try again later'
      });
    }
    
    if (errorMessage.includes('quota')) {
      return res.status(429).json({ 
        error: 'Quota exceeded',
        details: 'API quota has been exceeded'
      });
    }

    if (errorMessage.includes('not available')) {
      return res.status(400).json({ 
        error: 'Image generation not available',
        details: 'Image generation may not be available in your region'
      });
    }

    return res.status(500).json({ 
      error: 'Failed to generate image',
      details: errorMessage
    });
  }
}
