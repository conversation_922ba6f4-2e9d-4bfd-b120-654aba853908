# Google Gemini API Documentation

## Overview

This documentation covers the integration of Google's Gemini API for text and image generation capabilities. The Gemini API provides powerful AI models for generating content, understanding multimodal inputs, and creating images.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Text Generation](#text-generation)
3. [Image Generation](#image-generation)
4. [Authentication](#authentication)
5. [Models Available](#models-available)
6. [Code Examples](#code-examples)
7. [Best Practices](#best-practices)
8. [<PERSON><PERSON><PERSON> Handling](#error-handling)

## Getting Started

### Prerequisites

- Google AI API Key (Get from [Google AI Studio](https://aistudio.google.com/apikey))
- Node.js environment for JavaScript implementation
- Install the Google GenAI SDK

### Installation

```bash
npm install @google/genai
```

### Basic Setup

```javascript
import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({ apiKey: "YOUR_API_KEY" });
```

## Text Generation

### Basic Text Generation

The Gemini API can generate text from various inputs including text, images, video, and audio.

#### Simple Text Generation

```javascript
import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({ apiKey: "GEMINI_API_KEY" });

async function generateText() {
  const response = await ai.models.generateContent({
    model: "gemini-2.0-flash",
    contents: "How does AI work?",
  });
  console.log(response.text);
}
```

#### With System Instructions

```javascript
async function generateWithInstructions() {
  const response = await ai.models.generateContent({
    model: "gemini-2.0-flash",
    contents: "Hello there",
    config: {
      systemInstruction: "You are a helpful assistant. Your name is Claude.",
      maxOutputTokens: 500,
      temperature: 0.1,
    },
  });
  console.log(response.text);
}
```

#### Streaming Responses

```javascript
async function streamingGeneration() {
  const response = await ai.models.generateContentStream({
    model: "gemini-2.0-flash",
    contents: "Explain how AI works",
  });

  for await (const chunk of response) {
    console.log(chunk.text);
  }
}
```

#### Multi-turn Conversations (Chat)

```javascript
async function chatExample() {
  const chat = ai.chats.create({
    model: "gemini-2.0-flash",
    history: [
      {
        role: "user",
        parts: [{ text: "Hello" }],
      },
      {
        role: "model",
        parts: [{ text: "Great to meet you. What would you like to know?" }],
      },
    ],
  });

  const response1 = await chat.sendMessage({
    message: "I have 2 dogs in my house.",
  });
  console.log("Chat response 1:", response1.text);

  const response2 = await chat.sendMessage({
    message: "How many paws are in my house?",
  });
  console.log("Chat response 2:", response2.text);
}
```

## Image Generation

### Gemini Native Image Generation

Gemini 2.0 Flash can generate and edit images natively within conversations.

#### Text to Image

```javascript
import { GoogleGenAI, Modality } from "@google/genai";
import * as fs from "node:fs";

async function generateImage() {
  const ai = new GoogleGenAI({ apiKey: "GEMINI_API_KEY" });

  const contents =
    "Hi, can you create a 3d rendered image of a pig " +
    "with wings and a top hat flying over a happy " +
    "futuristic scifi city with lots of greenery?";

  const response = await ai.models.generateContent({
    model: "gemini-2.0-flash-preview-image-generation",
    contents: contents,
    config: {
      responseModalities: [Modality.TEXT, Modality.IMAGE],
    },
  });

  for (const part of response.candidates[0].content.parts) {
    if (part.text) {
      console.log(part.text);
    } else if (part.inlineData) {
      const imageData = part.inlineData.data;
      const buffer = Buffer.from(imageData, "base64");
      fs.writeFileSync("gemini-native-image.png", buffer);
      console.log("Image saved as gemini-native-image.png");
    }
  }
}
```

#### Image Editing (Text + Image to Image)

```javascript
async function editImage() {
  const ai = new GoogleGenAI({ apiKey: "GEMINI_API_KEY" });

  // Load the image from the local file system
  const imagePath = "path/to/image.png";
  const imageData = fs.readFileSync(imagePath);
  const base64Image = imageData.toString("base64");

  const contents = [
    { text: "Can you add a llama next to the image?" },
    {
      inlineData: {
        mimeType: "image/png",
        data: base64Image,
      },
    },
  ];

  const response = await ai.models.generateContent({
    model: "gemini-2.0-flash-preview-image-generation",
    contents: contents,
    config: {
      responseModalities: [Modality.TEXT, Modality.IMAGE],
    },
  });

  for (const part of response.candidates[0].content.parts) {
    if (part.text) {
      console.log(part.text);
    } else if (part.inlineData) {
      const imageData = part.inlineData.data;
      const buffer = Buffer.from(imageData, "base64");
      fs.writeFileSync("gemini-edited-image.png", buffer);
      console.log("Image saved as gemini-edited-image.png");
    }
  }
}
```

### Imagen 3 Image Generation

For specialized image generation tasks requiring high quality and specific styles.

```javascript
async function generateWithImagen() {
  const ai = new GoogleGenAI({ apiKey: "GEMINI_API_KEY" });

  const response = await ai.models.generateImages({
    model: 'imagen-3.0-generate-002',
    prompt: 'Robot holding a red skateboard',
    config: {
      numberOfImages: 4,
      aspectRatio: "1:1", // Options: "1:1", "3:4", "4:3", "9:16", "16:9"
      personGeneration: "allow_adult", // "dont_allow", "allow_adult", "allow_all"
    },
  });

  let idx = 1;
  for (const generatedImage of response.generatedImages) {
    let imgBytes = generatedImage.image.imageBytes;
    const buffer = Buffer.from(imgBytes, "base64");
    fs.writeFileSync(`imagen-${idx}.png`, buffer);
    idx++;
  }
}
```

## Authentication

### API Key Setup

1. Get your API key from [Google AI Studio](https://aistudio.google.com/apikey)
2. Store it securely (use environment variables)

```javascript
// Using environment variables
const ai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
```

## Models Available

### Text Generation Models
- `gemini-2.0-flash` - Latest multimodal model with enhanced capabilities
- `gemini-2.5-flash-preview` - Experimental model with new features
- `gemini-2.5-pro-preview` - Most powerful reasoning model

### Image Generation Models
- `gemini-2.0-flash-preview-image-generation` - Native image generation within Gemini
- `imagen-3.0-generate-002` - Specialized high-quality image generation

## Best Practices

### Text Generation
1. **Use clear, descriptive prompts**
2. **Implement streaming for better UX**
3. **Set appropriate temperature values** (0.1 for factual, 0.9 for creative)
4. **Use system instructions for consistent behavior**
5. **Implement proper error handling**

### Image Generation
1. **Choose the right model**:
   - Use **Gemini** for contextual images and conversational editing
   - Use **Imagen 3** for high-quality, artistic, or specific style requirements

2. **Prompt Engineering**:
   - Be descriptive and clear
   - Include subject, context, and style
   - Use specific photography terms for photorealistic results
   - Keep text in images under 25 characters

3. **Aspect Ratios**:
   - `1:1` - Square (social media)
   - `4:3` - Standard (photography)
   - `16:9` - Widescreen (landscapes)
   - `9:16` - Portrait (mobile, stories)

## Error Handling

```javascript
async function generateWithErrorHandling() {
  try {
    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: "Your prompt here",
    });
    return response.text;
  } catch (error) {
    console.error("Error generating content:", error);
    
    if (error.status === 429) {
      console.log("Rate limit exceeded. Please try again later.");
    } else if (error.status === 400) {
      console.log("Invalid request. Check your prompt and parameters.");
    } else if (error.status === 403) {
      console.log("API key invalid or insufficient permissions.");
    }
    
    throw error;
  }
}
```

## Rate Limits and Pricing

- Check current rate limits and pricing at [Google AI Pricing](https://ai.google.dev/pricing)
- Implement proper rate limiting in your application
- Consider using context caching for repeated prompts

## Additional Resources

- [Official Gemini API Documentation](https://ai.google.dev/gemini-api/docs)
- [Google AI Studio](https://aistudio.google.com) - Test and prototype
- [Gemini API Cookbook](https://github.com/google-gemini/cookbook) - Examples and tutorials
- [Community Forum](https://discuss.ai.google.dev/c/gemini-api/) - Support and discussions

## Notes

- Image generation may not be available in all regions
- Gemini supports multiple languages but performs best in English
- All generated images include SynthID watermarking
- Consider implementing content filtering for production applications
