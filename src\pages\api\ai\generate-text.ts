import { NextApiRequest, NextApiResponse } from 'next';
import { geminiService } from '@/lib/gemini';
import { debugLog } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      prompt,
      model,
      systemInstruction,
      temperature,
      maxOutputTokens,
      streaming
    } = req.body;

    // Validate required fields
    if (!prompt || typeof prompt !== 'string') {
      return res.status(400).json({ error: 'Prompt is required and must be a string' });
    }

    debugLog('AI Text Generation Request', {
      promptLength: prompt.length,
      model: model || 'default',
      temperature: temperature || 'default',
      maxOutputTokens: maxOutputTokens || 'default'
    });

    // Generate text using Gemini service
    const response = await geminiService.generateText({
      prompt,
      model,
      systemInstruction,
      temperature,
      maxOutputTokens,
      streaming: false // For API endpoint, we'll use non-streaming
    });

    debugLog('AI Text Generation Success', {
      responseLength: response.text?.length || 0
    });

    return res.status(200).json({
      success: true,
      text: response.text,
      model: model || 'gemini-2.0-flash',
      metadata: {
        promptLength: prompt.length,
        responseLength: response.text?.length || 0,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    debugLog('AI Text Generation Error', { error });
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    // Handle specific error types
    if (errorMessage.includes('API key')) {
      return res.status(401).json({ 
        error: 'Invalid API key or authentication failed',
        details: 'Please check your Gemini API key configuration'
      });
    }
    
    if (errorMessage.includes('rate limit')) {
      return res.status(429).json({ 
        error: 'Rate limit exceeded',
        details: 'Please try again later'
      });
    }
    
    if (errorMessage.includes('quota')) {
      return res.status(429).json({ 
        error: 'Quota exceeded',
        details: 'API quota has been exceeded'
      });
    }

    return res.status(500).json({ 
      error: 'Failed to generate text',
      details: errorMessage
    });
  }
}
