import { NextApiRequest, NextApiResponse } from 'next';
import { geminiService } from '@/lib/gemini';
import { debugLog } from '@/lib/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    debugLog('AI Health Check Request');

    // Perform health check
    const isHealthy = await geminiService.healthCheck();

    debugLog('AI Health Check Result', { isHealthy });

    if (isHealthy) {
      return res.status(200).json({
        success: true,
        status: 'healthy',
        message: 'Gemini AI service is operational',
        timestamp: new Date().toISOString()
      });
    } else {
      return res.status(503).json({
        success: false,
        status: 'unhealthy',
        message: 'Gemini AI service is not responding properly',
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    debugLog('AI Health Check Error', { error });
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    return res.status(503).json({
      success: false,
      status: 'error',
      message: 'Health check failed',
      error: errorMessage,
      timestamp: new Date().toISOString()
    });
  }
}
